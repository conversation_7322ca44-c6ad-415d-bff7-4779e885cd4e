/**
*   Simulador de Navegação entre Asteroides
*
*   Funcionalidades:
*   - Câmera controlável como nave espacial
*   - Sistema de asteroides 3D com iluminação
*   - Movimentação baseada em vetores
*   - Controle de FPS
*   - Alternância wireframe/preenchimento
*   - Iluminação dinâmica
*
*   Controles:
*   - WASD: Movimento da câmera
*   - Mouse: Rotação da câmera
*   - F: Wireframe/Fill
*   - R: Reset câmera
*   - ESC: Sair
*
**/

#include <GL/glut.h>
#include <stdlib.h>
#include <stdio.h>
#include <vector>
#include <ctime>
#include <iostream>

// Includes das classes
#include "Config.h"
#include "Vector3.h"
#include "Camera.h"
#include "Asteroid.h"
#include "LODManager.h"
#include "TextureManager.h"
#include "GameMenu.h"
#include "Skybox.h"
#include "ProceduralAsteroid.h"
#include "Sun.h"
#include "Minimap.h"
#include "AudioManager.h"

// Variáveis globais
Camera camera(Vector3(CAMERA_INITIAL_X, CAMERA_INITIAL_Y, CAMERA_INITIAL_Z));
std::vector<Asteroid> asteroids;
LODManager lodManager;
TextureManager textureManager;
GameMenu gameMenu;
Skybox skybox;
ProceduralAsteroid asteroidGenerator;
Sun sun(Vector3(SUN_POSITION_X, SUN_POSITION_Y, SUN_POSITION_Z), SUN_SIZE);
Minimap minimap(SCREEN_X, SCREEN_Y);
AudioManager audioManager;
bool wireframeMode = false;
bool keys[256] = {false};
bool isFullscreen = true;
int windowWidth = SCREEN_X;
int windowHeight = SCREEN_Y;
int lastTime = 0;
float deltaTime = 0;

// Variáveis para movimento suave
Vector3 velocity(0, 0, 0);
float acceleration = DEFAULT_ACCELERATION;
float friction = DEFAULT_FRICTION;
float maxSpeed = DEFAULT_MAX_SPEED;

// Variáveis para controle de FPS
int frameCount = 0;
int lastFPSTime = 0;
float currentFPS = 0;

// Declaração das funções
void setupRenderMode();
void updateScene();
void renderScene();
void renderHUD();
void renderText(float x, float y, const char* text);

void setupRenderMode() {
    if (wireframeMode) {
        // Modo wireframe - força cor branca brilhante
        glDisable(GL_LIGHTING);
        glDisable(GL_COLOR_MATERIAL);
        glDisable(GL_LIGHT0);
        glDisable(GL_LIGHT1);
        glDisable(GL_LIGHT2);

        // Define cor branca pura para wireframe
        glColor3f(1.0f, 1.0f, 1.0f);

        // Configura material branco brilhante
        float brightWhite[] = {1.0f, 1.0f, 1.0f, 1.0f};
        glMaterialfv(GL_FRONT_AND_BACK, GL_AMBIENT, brightWhite);
        glMaterialfv(GL_FRONT_AND_BACK, GL_DIFFUSE, brightWhite);
        glMaterialfv(GL_FRONT_AND_BACK, GL_SPECULAR, brightWhite);
        glMaterialfv(GL_FRONT_AND_BACK, GL_EMISSION, brightWhite);

        glPolygonMode(GL_FRONT_AND_BACK, GL_LINE);
        // glLineWidth(1.5f); // Linhas um pouco mais grossas para melhor visibilidade
    } else {
        // Modo fill - DESABILITA COLOR_MATERIAL para usar cores dos materiais
        glColor3f(1.0f, 1.0f, 1.0f);
        glEnable(GL_LIGHTING);
        glDisable(GL_COLOR_MATERIAL); // IMPORTANTE: desabilita para usar cores dos materiais

        // Remove emissão para modo fill
        float noEmission[] = {0.0f, 0.0f, 0.0f, 1.0f};
        glMaterialfv(GL_FRONT_AND_BACK, GL_EMISSION, noEmission);

        glPolygonMode(GL_FRONT_AND_BACK, GL_FILL);
        glLineWidth(1.0f); // Restaura espessura padrão

        // Luz principal branca neutra
        float lightPos[] = {0.0f, 100.0f, 0.0f, 1.0f};
        float white[] = {1.0f, 1.0f, 1.0f, 1.0f};
        float ambient[] = {0.4f, 0.4f, 0.4f, 1.0f}; // Ambiente um pouco mais claro

        glLightfv(GL_LIGHT0, GL_POSITION, lightPos);
        glLightfv(GL_LIGHT0, GL_DIFFUSE, white);
        glLightfv(GL_LIGHT0, GL_AMBIENT, ambient);
        glEnable(GL_LIGHT0);

        sun.setupLighting();
    }
}

void resetOpenGLStates() {
    glColor3f(1.0f, 1.0f, 1.0f);
    glDisable(GL_LIGHTING);
    glDisable(GL_COLOR_MATERIAL);
    glDisable(GL_LIGHT0);
    glDisable(GL_LIGHT1);
    glDisable(GL_LIGHT2);
    glPolygonMode(GL_FRONT_AND_BACK, GL_FILL);
}

void init() {
    resetOpenGLStates();
    srand(time(NULL));

    // Configuração OpenGL básica
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    gluPerspective(60.0, (float)SCREEN_X/SCREEN_Y, 0.1, 1000.0);
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();
    glClearColor(0.02f, 0.02f, 0.05f, 1.0f); // Fundo mais escuro para contraste
    glEnable(GL_DEPTH_TEST);
    glColor3f(1.0f, 1.0f, 1.0f);

    // Inicializa componentes
    asteroids.resize(NUM_ASTEROIDS);
    textureManager.initialize();
    skybox.initialize(&textureManager);

    // Inicializa sistema de áudio
    if (audioManager.initialize()) {
        std::cout << "=== SISTEMA DE AUDIO PLAYSOUND ===" << std::endl;

        // Debug: mostra diretório atual
        char currentDir[MAX_PATH];
        if (GetCurrentDirectoryA(MAX_PATH, currentDir)) {
            std::cout << "Diretório atual: " << currentDir << std::endl;
        }

        // Testa diferentes caminhos para o arquivo
        std::vector<std::string> testPaths = {
            "audio/background.wav",
            "audio\\background.wav",
            "./audio/background.wav",
            ".\\audio\\background.wav",
            "D:\\Dev\\2025\\cg\\Demos\\t5-murilo-leal\\audio\\background.wav"
        };

        bool found = false;
        for (const auto& path : testPaths) {
            std::cout << "Testando caminho: " << path << std::endl;
            if (audioManager.loadMusic(path)) {
                std::cout << "✓ Arquivo encontrado! Iniciando música..." << std::endl;
                audioManager.playMusic(true); // true = loop
                found = true;
                break;
            }
        }

        if (!found) {
            std::cout << "⚠ Arquivo background.wav não encontrado em nenhum caminho testado" << std::endl;
            std::cout << "  Verifique se o arquivo existe na pasta audio/" << std::endl;
        }

        std::cout << "===================================" << std::endl;
    }

    // Gera asteroides procedurais
    for (int i = 0; i < NUM_ASTEROIDS; i++) {
        asteroids[i].generateProceduralMesh(asteroidGenerator);
    }

    // Configura modo inicial
    setupRenderMode();
}

void updateMovement() {
    Vector3 inputDirection(0, 0, 0);

    // Calcula direção de entrada baseada nas teclas
    if (keys['w'] || keys['W']) inputDirection = inputDirection + camera.direction;
    if (keys['s'] || keys['S']) inputDirection = inputDirection - camera.direction;
    if (keys['a'] || keys['A']) inputDirection = inputDirection - camera.right;
    if (keys['d'] || keys['D']) inputDirection = inputDirection + camera.right;
    if (keys['q'] || keys['Q']) inputDirection = inputDirection + camera.up;
    if (keys['e'] || keys['E']) inputDirection = inputDirection - camera.up;

    // Normaliza direção se há entrada
    if (inputDirection.length() > 0) {
        inputDirection = inputDirection.normalize();

        // Aplica aceleração
        velocity = velocity + inputDirection * acceleration * deltaTime;
    } else {
        // Aplica fricção quando não há entrada
        velocity = velocity * (1.0f - friction * deltaTime);
    }

    // Limita velocidade máxima
    if (velocity.length() > maxSpeed) {
        velocity = velocity.normalize() * maxSpeed;
    }

    // Aplica movimento
    Vector3 newPosition = camera.position + velocity * deltaTime;

    // Verificação básica de colisão com asteroides
    bool collision = false;
    for (const auto& asteroid : asteroids) {
        Vector3 diff = newPosition - asteroid.position;
        float distance = diff.length();
        if (distance < asteroid.size * 2.0f) { // Margem de segurança
            collision = true;
            break;
        }
    }

    // Verificação de colisão com o sol
    if (!collision) {
        Vector3 sunDiff = newPosition - sun.getPosition();
        float sunDistance = sunDiff.length();
        if (sunDistance < sun.getSize() * 2.0f) { // Margem de segurança para o sol
            collision = true;
        }
    }

    // Só aplica movimento se não houver colisão
    if (!collision) {
        camera.position = newPosition;
    } else {
        // Para a velocidade em caso de colisão
        velocity = velocity * 0.1f;
    }
}

void updateAsteroids() {
    for (auto& asteroid : asteroids) {
        asteroid.update();
    }
}

void calculateFPS() {
    frameCount++;
    int currentTime = glutGet(GLUT_ELAPSED_TIME);

    if (currentTime - lastFPSTime >= 1000) { // Atualiza FPS a cada segundo
        currentFPS = frameCount * 1000.0f / (currentTime - lastFPSTime);
        frameCount = 0;
        lastFPSTime = currentTime;
    }
}

void updateScene() {
    int currentTime = glutGet(GLUT_ELAPSED_TIME);
    deltaTime = (currentTime - lastTime) * 0.001f;
    lastTime = currentTime;

    if (deltaTime > 0.05f) deltaTime = 0.05f;

    updateMovement();
    updateAsteroids();
}

void renderScene() {
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();
    glColor3f(1.0f, 1.0f, 1.0f);

    camera.applyLookAt();
    setupRenderMode();

    // Renderiza skybox
    skybox.render(camera);

    // Renderiza o sol
    sun.update(deltaTime);
    sun.render();

    lodManager.resetStats();

    // Renderiza asteroides
    for (auto& asteroid : asteroids) {
        if (wireframeMode) {
            glColor3f(1.0f, 1.0f, 1.0f);
            glDisable(GL_LIGHTING);
        }
        asteroid.render(wireframeMode, lodManager, camera.position, &textureManager);
        if (wireframeMode) {
            glColor3f(1.0f, 1.0f, 1.0f);
        }
    }

    // Renderiza HUD e minimapa
    renderHUD();
    minimap.render(camera, asteroids, sun);
}

void renderText(float x, float y, const char* text) {
    GLboolean lightingEnabled = glIsEnabled(GL_LIGHTING);
    GLboolean depthTestEnabled = glIsEnabled(GL_DEPTH_TEST);
    GLboolean colorMaterialEnabled = glIsEnabled(GL_COLOR_MATERIAL);

    glDisable(GL_LIGHTING);
    glDisable(GL_DEPTH_TEST);
    glDisable(GL_COLOR_MATERIAL);

    glMatrixMode(GL_PROJECTION);
    glPushMatrix();
    glLoadIdentity();
    glOrtho(0, SCREEN_X, 0, SCREEN_Y, -1, 1);

    glMatrixMode(GL_MODELVIEW);
    glPushMatrix();
    glLoadIdentity();

    glColor3f(1.0f, 1.0f, 1.0f);
    glRasterPos2f(x, y);

    for (const char* c = text; *c != '\0'; c++) {
        glutBitmapCharacter(GLUT_BITMAP_HELVETICA_12, *c);
    }

    glPopMatrix();
    glMatrixMode(GL_PROJECTION);
    glPopMatrix();
    glMatrixMode(GL_MODELVIEW);

    if (depthTestEnabled) glEnable(GL_DEPTH_TEST);
    if (lightingEnabled) glEnable(GL_LIGHTING);
    if (colorMaterialEnabled) glEnable(GL_COLOR_MATERIAL);
}



void renderHUD() {
    char fpsText[50];
    char posText[100];
    char speedText[50];
    char lodText[100];
    char perfText[100];

    const LODStats& stats = lodManager.getStats();

    sprintf(fpsText, "FPS: %.1f", currentFPS);
    sprintf(posText, "Pos: (%.1f, %.1f, %.1f)", camera.position.x, camera.position.y, camera.position.z);
    sprintf(speedText, "Velocidade: %.1f", velocity.length());
    sprintf(lodText, "LOD: VH:%d H:%d M:%d L:%d VL:%d",
            stats.lodCounts[0], stats.lodCounts[1], stats.lodCounts[2],
            stats.lodCounts[3], stats.lodCounts[4]);
    sprintf(perfText, "Renderizados: %d | Culled: %d | Dist.Media: %.1f",
            stats.objectsRendered, stats.objectsCulled, stats.averageDistance);

    renderText(10, SCREEN_Y - 20, fpsText);
    renderText(10, SCREEN_Y - 40, posText);
    renderText(10, SCREEN_Y - 60, speedText);
    renderText(10, SCREEN_Y - 80, wireframeMode ? "Modo: Wireframe" : "Modo: Preenchido");
    renderText(10, SCREEN_Y - 100, textureManager.isEnabled() ? "Texturas: ATIVAS" : "Texturas: INATIVAS");

    int proceduralCount = 0;
    for (const auto& asteroid : asteroids) {
        if (asteroid.useProceduralMesh) proceduralCount++;
    }

    char proceduralText[100];
    sprintf(proceduralText, "Asteroides Procedurais: %d/%d", proceduralCount, NUM_ASTEROIDS);
    renderText(10, SCREEN_Y - 120, proceduralText);

    char cacheText[100];
    sprintf(cacheText, "Cache Malhas: %d", asteroidGenerator.getCacheSize());
    renderText(10, SCREEN_Y - 140, cacheText);

    renderText(10, SCREEN_Y - 160, lodText);
    renderText(10, SCREEN_Y - 180, perfText);

    if (lodManager.isDebugMode()) {
        renderText(10, SCREEN_Y - 200, "Debug LOD: ATIVO");
    }

    // Informações de áudio
    if (audioManager.isInitialized()) {
        char audioText[100];
        const char* audioStatus = audioManager.isMusicPlaying() ?
            (audioManager.isMusicPaused() ? "PAUSADA" : "TOCANDO") : "PARADA";
        sprintf(audioText, "Audio: %s | Volume: %.0f%%",
            audioStatus, audioManager.getVolume() * 100);
        renderText(10, SCREEN_Y - 220, audioText);
    }

    Asteroid* closest = nullptr;
    float closestDist = 1000000.0f;
    for (auto& asteroid : asteroids) {
        float dist = lodManager.calculateDistance(asteroid.position, camera.position);
        if (dist < closestDist) {
            closestDist = dist;
            closest = &asteroid;
        }
    }

    if (closest && closestDist < 20.0f) {
        char infoText[200];
        if (closest->useProceduralMesh) {
            sprintf(infoText, "Proximo: %s %s (%.1f)",
                ProceduralAsteroid::getShapeName(closest->proceduralParams.shape),
                ProceduralAsteroid::getCompositionName(closest->proceduralParams.composition),
                closestDist);
        } else {
            const char* types[] = {"Esfera", "Cubo", "Torus", "Teapot"};
            sprintf(infoText, "Proximo: %s Classico (%.1f)", types[closest->type], closestDist);
        }
        renderText(10, SCREEN_Y - 240, infoText);
    }
}

void display() {
    calculateFPS();

    // Controla áudio baseado no estado do menu
    static bool wasMenuActive = false;
    bool isMenuActive = gameMenu.isMenuActive();

    if (isMenuActive != wasMenuActive) {
        if (isMenuActive) {
            // Entrando no menu - pausa música
            if (audioManager.isMusicPlaying()) {
                audioManager.pauseMusic();
            }
        } else {
            // Saindo do menu - retoma música
            if (audioManager.isMusicPaused()) {
                audioManager.resumeMusic();
            }
        }
        wasMenuActive = isMenuActive;
    }

    if (gameMenu.isMenuActive()) {
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
        gameMenu.render();
        glutSwapBuffers();
        return;
    }

    updateScene();
    renderScene();
    glutSwapBuffers();
}


void keyboard(unsigned char key, int x, int y) {
    if (gameMenu.isMenuActive()) {
        gameMenu.handleKeyboard(key);
        return;
    }

    keys[key] = true;

    switch(key) {
        case 27:
            gameMenu.setState(GAME_PAUSED);
            break;
        case 'f': case 'F': // Toggle wireframe/fill
            wireframeMode = !wireframeMode;
            setupRenderMode();
            break;
        case 'r': case 'R': // Reset camera position
            camera = Camera(Vector3(CAMERA_INITIAL_X, CAMERA_INITIAL_Y, CAMERA_INITIAL_Z));
            velocity = Vector3(0, 0, 0);
            break;
        case '+': case '=': // Aumenta velocidade
            maxSpeed += 5.0f;
            break;
        case '-': case '_': // Diminui velocidade
            maxSpeed = maxSpeed > 5.0f ? maxSpeed - 5.0f : 5.0f;
            break;
        case 'h': case 'H':
            break;
        case 'l': case 'L': // Toggle LOD debug
            lodManager.setDebugMode(!lodManager.isDebugMode());
            break;
        case '1': // Configuração LOD próxima
            {
                LODConfig config = lodManager.getConfig();
                for (int i = 0; i < 5; i++) {
                    config.distances[i] *= 0.7f;
                }
                lodManager.setConfig(config);
            }
            break;
        case '2': // Configuração LOD média
            {
                LODConfig config;
                lodManager.setConfig(config);
            }
            break;
        case '3': // Configuração LOD distante
            {
                LODConfig config = lodManager.getConfig();
                for (int i = 0; i < 5; i++) {
                    config.distances[i] *= 1.5f;
                }
                lodManager.setConfig(config);
            }
            break;
        case 't': case 'T':
            textureManager.setEnabled(!textureManager.isEnabled());
            break;
        case 'g': case 'G':
            for (auto& asteroid : asteroids) {
                asteroid.generateProceduralMesh(asteroidGenerator);
            }
            break;
        case 'm': case 'M': // Toggle minimapa
            minimap.setEnabled(!minimap.isEnabled());
            break;
        case 'k': case 'K': // Toggle skybox
            skybox.setEnabled(!skybox.isEnabled());
            break;
        case 'x': case 'X': // Reset completo dos estados OpenGL
            resetOpenGLStates();
            setupRenderMode();
            break;
        case 'p': case 'P': // Toggle play/pause música
            if (audioManager.isMusicPlaying()) {
                audioManager.pauseMusic();
            } else if (audioManager.isMusicPaused()) {
                audioManager.resumeMusic();
            }
            break;
        case '[': // Diminui volume
            audioManager.setVolume(audioManager.getVolume() - 0.1f);
            break;
        case ']': // Aumenta volume
            audioManager.setVolume(audioManager.getVolume() + 0.1f);
            break;

    }
}

void keyboardUp(unsigned char key, int x, int y) {
    if (!gameMenu.isMenuActive()) {
        keys[key] = false;
    }
}

void reshape(int width, int height) {
    windowWidth = width;
    windowHeight = height;

    glViewport(0, 0, width, height);

    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    gluPerspective(60.0, (double)width / (double)height, 0.1, 1000.0);

    glMatrixMode(GL_MODELVIEW);

    // Atualiza o minimapa com o novo tamanho da tela
    minimap.updateScreenSize(width, height);
}

void specialKeys(int key, int x, int y) {
    if (gameMenu.isMenuActive()) {
        gameMenu.handleSpecialKeys(key);
        return;
    }

    switch(key) {
        case GLUT_KEY_F11: // Toggle fullscreen
            isFullscreen = !isFullscreen;
            if (isFullscreen) {
                glutFullScreen();
            } else {
                glutReshapeWindow(SCREEN_X, SCREEN_Y);
                glutPositionWindow(100, 100);
            }
            break;
    }
}

void mouseMotion(int x, int y) {
    if (gameMenu.isMenuActive()) return;

    static int centerX = SCREEN_X / 2;
    static int centerY = SCREEN_Y / 2;
    static bool firstMouse = true;

    if (firstMouse) {
        firstMouse = false;
        return;
    }

    float deltaX = (x - centerX) * ROTATION_SPEED * 2.0f;
    float deltaY = (centerY - y) * ROTATION_SPEED * 2.0f;

    camera.rotate(deltaX, deltaY);

    glutWarpPointer(centerX, centerY);
}

void mouseClick(int button, int state, int x, int y) {
}

void printInstructions() {
    printf("=== SIMULADOR DE NAVEGACAO ENTRE ASTEROIDES ===\n");
    printf("Controles:\n");
    printf("  WASD     - Movimento da camera (frente/tras/esquerda/direita)\n");
    printf("  QE       - Movimento vertical (cima/baixo)\n");
    printf("  Mouse    - Rotacao da camera (movimento livre)\n");
    printf("  F        - Alternar wireframe/preenchimento\n");
    printf("  R        - Reset posicao da camera\n");
    printf("  +/-      - Aumentar/diminuir velocidade maxima\n");
    printf("  L        - Toggle debug LOD (mostra nivel de cada asteroide)\n");
    printf("  1/2/3    - Configuracoes LOD (proxima/padrao/distante)\n");
    printf("  T        - Toggle texturas\n");
    printf("  G        - Regenerar asteroides procedurais\n");
    printf("  M        - Toggle minimapa\n");
    printf("  X        - Reset estados OpenGL\n");
    printf("  P        - Play/Pause musica\n");
    printf("  [ ]      - Diminuir/Aumentar volume\n");
    printf("  F11      - Alternar fullscreen/janela\n");
    printf("  ESC      - Abrir menu de pausa\n");
    printf("\nCaracteristicas:\n");
    printf("  - Movimento suave com aceleracao e friccao\n");
    printf("  - Sol central como fonte de luz principal\n");
    printf("  - Skybox com texturas espaciais\n");
    printf("  - %d asteroides com formas variadas\n", NUM_ASTEROIDS);
    printf("  - Sistema LOD (Level of Detail) para otimizacao\n");
    printf("  - HUD com informacoes de FPS, posicao, velocidade e LOD\n");
    printf("  - Minimapa 2D mostrando posicao relativa dos objetos\n");
    printf("  - Carregamento de texturas de imagens\n");
    printf("  - Controle de camera baseado em vetores matematicos\n");
    printf("  - Culling automatico por distancia\n");
    printf("\nSistema LOD:\n");
    printf("  - 5 niveis de detalhe baseados na distancia\n");
    printf("  - Culling automatico de objetos distantes\n");
    printf("  - Estatisticas em tempo real no HUD\n");
    printf("\nNavegue pela cena como se fosse uma nave espacial!\n");
    printf("===============================================\n\n");
}

int main(int argc, char** argv) {
    glutInit(&argc, argv);
    glutInitDisplayMode(GLUT_DOUBLE | GLUT_RGB | GLUT_DEPTH);
    glutInitWindowSize(SCREEN_X, SCREEN_Y);
    glutInitWindowPosition(100, 100);
    glutCreateWindow("Simulador de Navegacao entre Asteroides");

    // Inicia em modo fullscreen
    glutFullScreen();

    init();
    printInstructions();

    // Registra callbacks
    glutDisplayFunc(display);
    glutIdleFunc(display);
    glutReshapeFunc(reshape);
    glutKeyboardFunc(keyboard);
    glutKeyboardUpFunc(keyboardUp);
    glutSpecialFunc(specialKeys);
    glutMotionFunc(mouseMotion);
    glutPassiveMotionFunc(mouseMotion);
    glutMouseFunc(mouseClick);

    glutSetCursor(GLUT_CURSOR_LEFT_ARROW);

    // Inicializa timer
    lastTime = glutGet(GLUT_ELAPSED_TIME);

    glutMainLoop();
    return 0;
}
