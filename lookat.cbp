<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="lookat" />
		<Option pch_mode="2" />
		<Option compiler="gcc" />
		<Build>
			<Target title="Debug">
				<Option output="../__bin/Debug/lookat" prefix_auto="1" extension_auto="1" />
				<Option working_dir="../" />
				<Option object_output="../__obj/Debug/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-g" />
				</Compiler>
			</Target>
			<Target title="Release">
				<Option output="../__bin/Release/lookat" prefix_auto="1" extension_auto="1" />
				<Option working_dir="../" />
				<Option object_output="../__obj/Release/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-O2 -Wall" />
				</Compiler>
				<Linker>
					<Add option="-s" />
				</Linker>
			</Target>
		</Build>
		<Compiler>
			<Add option="-Wall" />
			<Add option="-fexceptions" />
			<Add option="-std=c++11" />
			<Add directory="../include" />
		</Compiler>
		<Linker>
			<Add option="-lwinmm" />
			<Add library="../lib/libfreeglut32.a" />
			<Add library="../lib/libopengl32.a" />
			<Add library="../lib/libglu32.a" />
		</Linker>
		<Unit filename="src/Asteroid.h" />
		<Unit filename="src/AudioManager.cpp" />
		<Unit filename="src/AudioManager.h" />
		<Unit filename="src/AudioManager_PlaySound.cpp" />
		<Unit filename="src/AudioManager_Simple.cpp" />
		<Unit filename="src/Camera.h" />
		<Unit filename="src/Config.h" />
		<Unit filename="src/GameMenu.cpp" />
		<Unit filename="src/GameMenu.h" />
		<Unit filename="src/LODManager.cpp" />
		<Unit filename="src/LODManager.h" />
		<Unit filename="src/Mesh.cpp" />
		<Unit filename="src/Mesh.h" />
		<Unit filename="src/Minimap.cpp" />
		<Unit filename="src/Minimap.h" />
		<Unit filename="src/ProceduralAsteroid.cpp" />
		<Unit filename="src/ProceduralAsteroid.h" />
		<Unit filename="src/Skybox.cpp" />
		<Unit filename="src/Skybox.h" />
		<Unit filename="src/Sun.cpp" />
		<Unit filename="src/Sun.h" />
		<Unit filename="src/TextureManager.cpp" />
		<Unit filename="src/TextureManager.h" />
		<Unit filename="src/Vector3.h" />
		<Unit filename="src/lookat.cpp" />
		<Unit filename="src/stb_image.h" />
		<Extensions>
			<code_completion />
			<envvars />
			<debugger />
		</Extensions>
	</Project>
</CodeBlocks_project_file>
