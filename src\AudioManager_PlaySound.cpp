// Versão alternativa do AudioManager usando PlaySound (mais simples e confiável)
#include "AudioManager.h"
#include <iostream>

#ifdef _WIN32
#include <thread>
#include <atomic>

// Variáveis globais para controle de thread
std::atomic<bool> shouldStopMusic(false);
std::atomic<bool> musicThreadRunning(false);
std::thread musicThread;
std::string globalMusicFile;
#endif

AudioManager::AudioManager() 
    : initialized(false), musicPlaying(false), musicPaused(false), volume(0.7f) {
}

AudioManager::~AudioManager() {
    cleanup();
}

bool AudioManager::initialize() {
    if (initialized) return true;
    
    initialized = true;
    std::cout << "AudioManager: Inicializado com PlaySound" << std::endl;
    return true;
}

void AudioManager::cleanup() {
    if (!initialized) return;
    
    stopMusic();
    initialized = false;
    std::cout << "AudioManager: Finalizado" << std::endl;
}

bool AudioManager::loadMusic(const std::string& filename) {
    if (!initialized) {
        std::cout << "AudioManager: Não inicializado!" << std::endl;
        return false;
    }

    // Para o áudio atual se estiver tocando
    stopMusic();

    // Converte para caminho absoluto
#ifdef _WIN32
    char fullPath[MAX_PATH];
    if (GetFullPathNameA(filename.c_str(), MAX_PATH, fullPath, NULL) == 0) {
        std::cout << "AudioManager: Erro ao obter caminho completo para: " << filename << std::endl;
        return false;
    }

    currentMusicFile = std::string(fullPath);
    std::cout << "AudioManager: Música carregada: " << currentMusicFile << std::endl;
    
    // Verifica se o arquivo existe
    DWORD fileAttr = GetFileAttributesA(currentMusicFile.c_str());
    if (fileAttr == INVALID_FILE_ATTRIBUTES) {
        std::cout << "AudioManager: ERRO - Arquivo não encontrado: " << currentMusicFile << std::endl;
        return false;
    }
    
    return true;
#else
    currentMusicFile = filename;
    return true;
#endif
}

void AudioManager::playMusic(bool loop) {
    if (!initialized || currentMusicFile.empty()) {
        std::cout << "AudioManager: Não é possível tocar música" << std::endl;
        return;
    }

#ifdef _WIN32
    // Para qualquer música que esteja tocando
    stopMusic();
    
    std::cout << "AudioManager: Iniciando música com PlaySound: " << currentMusicFile << std::endl;
    
    // Define flags para PlaySound
    DWORD flags = SND_FILENAME | SND_ASYNC;
    if (loop) {
        flags |= SND_LOOP;
    }
    
    // Tenta tocar o arquivo
    if (PlaySoundA(currentMusicFile.c_str(), NULL, flags)) {
        musicPlaying = true;
        musicPaused = false;
        std::cout << "AudioManager: ✓ Música iniciada com sucesso!" << (loop ? " (loop)" : "") << std::endl;
    } else {
        DWORD error = GetLastError();
        std::cout << "AudioManager: ERRO ao tocar música. Código de erro: " << error << std::endl;
        
        // Tenta diferentes flags
        std::cout << "AudioManager: Tentando sem loop..." << std::endl;
        if (PlaySoundA(currentMusicFile.c_str(), NULL, SND_FILENAME | SND_ASYNC)) {
            musicPlaying = true;
            musicPaused = false;
            std::cout << "AudioManager: ✓ Música iniciada sem loop!" << std::endl;
        } else {
            std::cout << "AudioManager: ERRO - Não foi possível tocar o arquivo" << std::endl;
        }
    }
#endif
}

void AudioManager::pauseMusic() {
#ifdef _WIN32
    if (!musicPlaying || musicPaused) return;
    
    // PlaySound não suporta pause diretamente, então para a música
    PlaySoundA(NULL, NULL, SND_PURGE);
    musicPaused = true;
    std::cout << "AudioManager: Música pausada" << std::endl;
#endif
}

void AudioManager::resumeMusic() {
#ifdef _WIN32
    if (!musicPlaying || !musicPaused) return;
    
    // Reinicia a música
    playMusic(true);
    musicPaused = false;
    std::cout << "AudioManager: Música retomada" << std::endl;
#endif
}

void AudioManager::stopMusic() {
#ifdef _WIN32
    if (!musicPlaying) return;
    
    PlaySoundA(NULL, NULL, SND_PURGE);
    musicPlaying = false;
    musicPaused = false;
    std::cout << "AudioManager: Música parada" << std::endl;
#endif
}

void AudioManager::setVolume(float vol) {
    volume = vol;
    if (volume < 0.0f) volume = 0.0f;
    if (volume > 1.0f) volume = 1.0f;
    
    std::cout << "AudioManager: Volume definido para " << (volume * 100) << "% (PlaySound não suporta controle de volume)" << std::endl;
}

void AudioManager::playSound(const std::string& filename) {
#ifdef _WIN32
    PlaySoundA(filename.c_str(), NULL, SND_FILENAME | SND_ASYNC);
    std::cout << "AudioManager: Efeito sonoro tocado: " << filename << std::endl;
#endif
}

void AudioManager::updateMusicState() {
    // Nada a fazer com PlaySound
}
