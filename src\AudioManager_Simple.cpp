// Versão simplificada do AudioManager para teste
// Use este arquivo se houver problemas com winmm

#include "AudioManager.h"
#include <iostream>

AudioManager::AudioManager() 
    : initialized(false), musicPlaying(false), musicPaused(false), volume(0.7f) {
}

AudioManager::~AudioManager() {
    cleanup();
}

bool AudioManager::initialize() {
    if (initialized) return true;
    
    // Versão simplificada - apenas simula o sistema de áudio
    initialized = true;
    std::cout << "AudioManager: Inicializado (modo simulação)" << std::endl;
    return true;
}

void AudioManager::cleanup() {
    if (!initialized) return;
    
    stopMusic();
    initialized = false;
    std::cout << "AudioManager: Finalizado" << std::endl;
}

bool AudioManager::loadMusic(const std::string& filename) {
    if (!initialized) {
        std::cout << "AudioManager: Não inicializado!" << std::endl;
        return false;
    }
    
    currentMusicFile = filename;
    std::cout << "AudioManager: Música carregada (simulação): " << filename << std::endl;
    return true;
}

void AudioManager::playMusic(bool loop) {
    if (!initialized || currentMusicFile.empty()) {
        std::cout << "AudioManager: Não é possível tocar música" << std::endl;
        return;
    }
    
    musicPlaying = true;
    musicPaused = false;
    std::cout << "AudioManager: Música iniciada (simulação)" << (loop ? " (loop)" : "") << std::endl;
}

void AudioManager::pauseMusic() {
    if (!musicPlaying || musicPaused) return;
    
    musicPaused = true;
    std::cout << "AudioManager: Música pausada (simulação)" << std::endl;
}

void AudioManager::resumeMusic() {
    if (!musicPlaying || !musicPaused) return;
    
    musicPaused = false;
    std::cout << "AudioManager: Música retomada (simulação)" << std::endl;
}

void AudioManager::stopMusic() {
    if (!musicPlaying) return;
    
    musicPlaying = false;
    musicPaused = false;
    std::cout << "AudioManager: Música parada (simulação)" << std::endl;
}

void AudioManager::setVolume(float vol) {
    volume = vol;
    if (volume < 0.0f) volume = 0.0f;
    if (volume > 1.0f) volume = 1.0f;
    
    std::cout << "AudioManager: Volume definido para " << (volume * 100) << "% (simulação)" << std::endl;
}

void AudioManager::playSound(const std::string& filename) {
    std::cout << "AudioManager: Efeito sonoro tocado (simulação): " << filename << std::endl;
}

void AudioManager::updateMusicState() {
    // Nada a fazer na versão simplificada
}
