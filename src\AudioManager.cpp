// AudioManager usando PlaySound (versão simplificada e confiável)
#include "AudioManager.h"
#include <iostream>

AudioManager::AudioManager()
    : initialized(false), musicPlaying(false), musicPaused(false), volume(0.7f) {
}

AudioManager::~AudioManager() {
    cleanup();
}

bool AudioManager::initialize() {
    if (initialized) return true;

    initialized = true;
    std::cout << "AudioManager: Inicializado com PlaySound" << std::endl;
    return true;
}

void AudioManager::cleanup() {
    if (!initialized) return;

    stopMusic();
    initialized = false;
    std::cout << "AudioManager: Finalizado" << std::endl;
}

bool AudioManager::loadMusic(const std::string& filename) {
    if (!initialized) {
        std::cout << "AudioManager: Não inicializado!" << std::endl;
        return false;
    }

    // Para o áudio atual se estiver tocando
    stopMusic();

#ifdef _WIN32
    // Primeiro tenta o caminho relativo diretamente
    DWORD fileAttr = GetFileAttributesA(filename.c_str());
    if (fileAttr != INVALID_FILE_ATTRIBUTES) {
        currentMusicFile = filename;
        std::cout << "AudioManager: Música carregada (caminho relativo): " << currentMusicFile << std::endl;

        // Verifica o tamanho do arquivo
        HANDLE hFile = CreateFileA(filename.c_str(), GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile != INVALID_HANDLE_VALUE) {
            LARGE_INTEGER fileSize;
            if (GetFileSizeEx(hFile, &fileSize)) {
                std::cout << "AudioManager: Tamanho do arquivo: " << fileSize.QuadPart << " bytes" << std::endl;
            }
            CloseHandle(hFile);
        }

        return true;
    }

    // Se não encontrar, tenta caminho absoluto
    char fullPath[MAX_PATH];
    if (GetFullPathNameA(filename.c_str(), MAX_PATH, fullPath, NULL) != 0) {
        fileAttr = GetFileAttributesA(fullPath);
        if (fileAttr != INVALID_FILE_ATTRIBUTES) {
            currentMusicFile = std::string(fullPath);
            std::cout << "AudioManager: Música carregada (caminho absoluto): " << currentMusicFile << std::endl;
            return true;
        }
    }

    std::cout << "AudioManager: ERRO - Arquivo não encontrado: " << filename << std::endl;
    return false;
#else
    currentMusicFile = filename;
    return true;
#endif
}

void AudioManager::playMusic(bool loop) {
    if (!initialized || currentMusicFile.empty()) {
        std::cout << "AudioManager: Não é possível tocar música" << std::endl;
        return;
    }

#ifdef _WIN32
    // Para qualquer música que esteja tocando
    stopMusic();

    std::cout << "AudioManager: Iniciando música com PlaySound: " << currentMusicFile << std::endl;

    // Teste simples primeiro - sem loop
    std::cout << "AudioManager: Testando reprodução simples..." << std::endl;
    if (PlaySoundA(currentMusicFile.c_str(), NULL, SND_FILENAME | SND_SYNC)) {
        std::cout << "AudioManager: ✓ Teste de reprodução bem-sucedido!" << std::endl;

        // Agora tenta com as flags corretas
        DWORD flags = SND_FILENAME | SND_ASYNC;
        if (loop) {
            flags |= SND_LOOP;
        }

        if (PlaySoundA(currentMusicFile.c_str(), NULL, flags)) {
            musicPlaying = true;
            musicPaused = false;
            std::cout << "AudioManager: ✓ Música iniciada com sucesso!" << (loop ? " (loop)" : "") << std::endl;
        } else {
            std::cout << "AudioManager: ERRO - Falha na reprodução assíncrona" << std::endl;
        }
    } else {
        DWORD error = GetLastError();
        std::cout << "AudioManager: ERRO - Arquivo WAV pode estar corrompido ou formato incompatível" << std::endl;
        std::cout << "AudioManager: Código de erro: " << error << std::endl;
        std::cout << "AudioManager: Certifique-se de que o arquivo é um WAV válido (PCM, 16-bit)" << std::endl;
    }
#endif
}



void AudioManager::pauseMusic() {
#ifdef _WIN32
    if (!musicPlaying || musicPaused) return;

    // PlaySound não suporta pause diretamente, então para a música
    PlaySoundA(NULL, NULL, SND_PURGE);
    musicPaused = true;
    std::cout << "AudioManager: Música pausada" << std::endl;
#endif
}

void AudioManager::resumeMusic() {
#ifdef _WIN32
    if (!musicPlaying || !musicPaused) return;

    // Reinicia a música
    playMusic(true);
    musicPaused = false;
    std::cout << "AudioManager: Música retomada" << std::endl;
#endif
}

void AudioManager::stopMusic() {
#ifdef _WIN32
    if (!musicPlaying) return;

    PlaySoundA(NULL, NULL, SND_PURGE);
    musicPlaying = false;
    musicPaused = false;
    std::cout << "AudioManager: Música parada" << std::endl;
#endif
}

void AudioManager::setVolume(float vol) {
    volume = vol;
    if (volume < 0.0f) volume = 0.0f;
    if (volume > 1.0f) volume = 1.0f;

    std::cout << "AudioManager: Volume definido para " << (volume * 100) << "% (PlaySound não suporta controle de volume)" << std::endl;
}

void AudioManager::playSound(const std::string& filename) {
#ifdef _WIN32
    PlaySoundA(filename.c_str(), NULL, SND_FILENAME | SND_ASYNC);
    std::cout << "AudioManager: Efeito sonoro tocado: " << filename << std::endl;
#endif
}

void AudioManager::updateMusicState() {
    // Nada a fazer com PlaySound
}
