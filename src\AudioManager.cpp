#include "AudioManager.h"
#include <iostream>
#include <sstream>

AudioManager::AudioManager()
    : initialized(false), musicPlaying(false), musicPaused(false), volume(0.7f) {
}

AudioManager::~AudioManager() {
    cleanup();
}

bool AudioManager::initialize() {
    if (initialized) return true;

#ifdef _WIN32
    // Windows Media Control Interface (MCI) já está disponível
    initialized = true;
    std::cout << "AudioManager: Inicializado com Windows MCI" << std::endl;
    return true;
#else
    std::cout << "AudioManager: Sistema de áudio não implementado para esta plataforma" << std::endl;
    return false;
#endif
}

void AudioManager::cleanup() {
    if (!initialized) return;

    stopMusic();
    initialized = false;
    std::cout << "AudioManager: Finalizado" << std::endl;
}

bool AudioManager::loadMusic(const std::string& filename) {
    if (!initialized) {
        std::cout << "AudioManager: Não inicializado!" << std::endl;
        return false;
    }

    // Para o áudio atual se estiver tocando
    stopMusic();

    // Converte para caminho absoluto
    char fullPath[MAX_PATH];
    if (GetFullPathNameA(filename.c_str(), MAX_PATH, fullPath, NULL) == 0) {
        std::cout << "AudioManager: Erro ao obter caminho completo para: " << filename << std::endl;
        return false;
    }

    currentMusicFile = std::string(fullPath);
    std::cout << "AudioManager: Música carregada: " << currentMusicFile << std::endl;

    // Verifica se o arquivo existe
    DWORD fileAttr = GetFileAttributesA(currentMusicFile.c_str());
    if (fileAttr == INVALID_FILE_ATTRIBUTES) {
        std::cout << "AudioManager: ERRO - Arquivo não encontrado: " << currentMusicFile << std::endl;
        return false;
    }

    return true;
}

void AudioManager::playMusic(bool loop) {
    if (!initialized || currentMusicFile.empty()) {
        std::cout << "AudioManager: Não é possível tocar música (inicializado=" << initialized
                  << ", arquivo=" << currentMusicFile << ")" << std::endl;
        return;
    }

#ifdef _WIN32
    std::cout << "AudioManager: Tentando tocar: " << currentMusicFile << std::endl;

    // Para qualquer música que esteja tocando
    mciSendStringA("close music", NULL, 0, NULL);

    // Tenta diferentes tipos de arquivo
    std::string openCmd;
    MCIERROR result;

    // Primeiro tenta como MP3
    openCmd = "open \"" + currentMusicFile + "\" type mpegvideo alias music";
    std::cout << "AudioManager: Comando MCI: " << openCmd << std::endl;
    result = mciSendStringA(openCmd.c_str(), NULL, 0, NULL);

    if (result != 0) {
        // Se falhar, tenta como WAV
        openCmd = "open \"" + currentMusicFile + "\" type waveaudio alias music";
        std::cout << "AudioManager: Tentando como WAV: " << openCmd << std::endl;
        result = mciSendStringA(openCmd.c_str(), NULL, 0, NULL);
    }

    if (result != 0) {
        // Se ainda falhar, tenta sem especificar tipo
        openCmd = "open \"" + currentMusicFile + "\" alias music";
        std::cout << "AudioManager: Tentando sem tipo: " << openCmd << std::endl;
        result = mciSendStringA(openCmd.c_str(), NULL, 0, NULL);
    }

    if (result != 0) {
        char errorBuffer[256];
        mciGetErrorStringA(result, errorBuffer, sizeof(errorBuffer));
        std::cout << "AudioManager: ERRO ao abrir música: " << errorBuffer << " (código: " << result << ")" << std::endl;
        return;
    }

    std::cout << "AudioManager: Arquivo aberto com sucesso!" << std::endl;

    // Define o volume
    int winVolume = (int)(volume * 1000);
    std::string volumeCmd = "setaudio music volume to " + std::to_string(winVolume);
    std::cout << "AudioManager: Definindo volume: " << volumeCmd << std::endl;
    mciSendStringA(volumeCmd.c_str(), NULL, 0, NULL);

    // Toca a música
    std::string playCmd = loop ? "play music repeat" : "play music";
    std::cout << "AudioManager: Comando play: " << playCmd << std::endl;
    result = mciSendStringA(playCmd.c_str(), NULL, 0, NULL);

    if (result == 0) {
        musicPlaying = true;
        musicPaused = false;
        std::cout << "AudioManager: ✓ Música iniciada com sucesso!" << (loop ? " (loop)" : "") << std::endl;
    } else {
        char errorBuffer[256];
        mciGetErrorStringA(result, errorBuffer, sizeof(errorBuffer));
        std::cout << "AudioManager: ERRO ao tocar música: " << errorBuffer << " (código: " << result << ")" << std::endl;
    }
#endif
}

void AudioManager::pauseMusic() {
    if (!musicPlaying || musicPaused) return;

#ifdef _WIN32
    MCIERROR result = mciSendStringA("pause music", NULL, 0, NULL);
    if (result == 0) {
        musicPaused = true;
        std::cout << "AudioManager: Música pausada" << std::endl;
    }
#endif
}

void AudioManager::resumeMusic() {
    if (!musicPlaying || !musicPaused) return;

#ifdef _WIN32
    MCIERROR result = mciSendStringA("resume music", NULL, 0, NULL);
    if (result == 0) {
        musicPaused = false;
        std::cout << "AudioManager: Música retomada" << std::endl;
    }
#endif
}

void AudioManager::stopMusic() {
    if (!musicPlaying) return;

#ifdef _WIN32
    mciSendStringA("stop music", NULL, 0, NULL);
    mciSendStringA("close music", NULL, 0, NULL);
#endif

    musicPlaying = false;
    musicPaused = false;
    std::cout << "AudioManager: Música parada" << std::endl;
}

void AudioManager::setVolume(float vol) {
    volume = vol;
    if (volume < 0.0f) volume = 0.0f;
    if (volume > 1.0f) volume = 1.0f;

#ifdef _WIN32
    if (musicPlaying) {
        int winVolume = (int)(volume * 1000);
        std::string volumeCmd = "setaudio music volume to " + std::to_string(winVolume);
        mciSendStringA(volumeCmd.c_str(), NULL, 0, NULL);
    }
#endif

    std::cout << "AudioManager: Volume definido para " << (volume * 100) << "%" << std::endl;
}

void AudioManager::playSound(const std::string& filename) {
    // Implementação futura para efeitos sonoros
#ifdef _WIN32
    // Pode usar PlaySound para efeitos curtos
    // PlaySoundA(filename.c_str(), NULL, SND_FILENAME | SND_ASYNC);
#endif
}

void AudioManager::updateMusicState() {
    // Verifica se a música ainda está tocando (para detectar fim da música)
#ifdef _WIN32
    if (musicPlaying && !musicPaused) {
        char statusBuffer[256];
        MCIERROR result = mciSendStringA("status music mode", statusBuffer, sizeof(statusBuffer), NULL);
        if (result == 0) {
            std::string status(statusBuffer);
            if (status.find("stopped") != std::string::npos) {
                musicPlaying = false;
                musicPaused = false;
            }
        }
    }
#endif
}
